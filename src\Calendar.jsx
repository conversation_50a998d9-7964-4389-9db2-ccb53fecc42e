import React, { useState } from "react";
import { MdKeyboardDoubleArrowRight } from "react-icons/md";
import { MdKeyboardDoubleArrowLeft } from "react-icons/md";
import { MdKeyboardArrowLeft } from "react-icons/md";
import { MdKeyboardArrowRight } from "react-icons/md";

function getDaysInMonth(year, month) {
  const date = new Date(year, month, 1);
  const days = [];
  while (date.getMonth() === month) {
    days.push(new Date(date));
    date.setDate(date.getDate() + 1);
  }
  return days;
}

// Helper function to compare dates
function isSameDate(date1, date2) {
  return date1.getDate() === date2.getDate() && 
         date1.getMonth() === date2.getMonth() && 
         date1.getFullYear() === date2.getFullYear();
}

// Helper function to check if a date is before the selected date (but not the selected date itself)
function isDateBeforeSelected(date, selectedDate) {
  if (!selectedDate) return false;
  const compareDate = new Date(date);
  const selected = new Date(selectedDate);
  compareDate.setHours(0, 0, 0, 0);
  selected.setHours(0, 0, 0, 0);
  return compareDate < selected; // Changed from <= to < to exclude the selected date
}

export default function Calendar({ onDateSelect }) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(null);

  const year = currentDate.getFullYear();
  const month = currentDate.getMonth();
  const days = getDaysInMonth(year, month);
  const startDay = new Date(year, month, 1).getDay();

  const prevMonth = () => setCurrentDate(new Date(year, month - 1, 1));
  const nextMonth = () => setCurrentDate(new Date(year, month + 1, 1));
  const prevYear = () => setCurrentDate(new Date(year - 1, month, 1));
  const nextYear = () => setCurrentDate(new Date(year + 1, month, 1));

  const handleDateClick = (clickedDate) => {
    setSelectedDate(clickedDate);
    if (onDateSelect) {
      onDateSelect(clickedDate);
    }
  };

  return (
    <div className="bg-white shadow-2xl rounded-2xl p-[20px] w-[380px] h-[277px] flex flex-col gap-2">
      {/* Header */}
      <div className="flex items-center justify-center gap-0 mb-2">
        <button className="text-md font-bold px-0 py-0 m-1" onClick={prevYear}>
        <MdKeyboardDoubleArrowLeft />
        </button>
        <button className="text-md font-bold px-0 py-0 m-1" onClick={prevMonth}>
        <MdKeyboardArrowLeft />
        </button>
        <span className="text-lg min-w-[120px] text-center text-gray-800">
          {currentDate.toLocaleString("default", { month: "long" })} {year}
        </span>
        <button className="text-md font-bold px-0 py-0 m-1" onClick={nextMonth}>
        <MdKeyboardArrowRight />
        </button>
        <button className="text-md font-bold px-0 py-0 m-1" onClick={nextYear}>
          <MdKeyboardDoubleArrowRight />
        </button>
      </div>

      {/* Calendar Grid */}
      <div className="grid grid-cols-7 text-md gap-1 text-center flex-1">
        {/* Empty cells for alignment */}
        {Array(startDay).fill(null).map((_, i) => (
          <div key={"empty" + i}></div>
        ))}
        {/* Days of the month */}
        {days.map((date) => {
          const isDisabled = selectedDate && isDateBeforeSelected(date, selectedDate);
          const isSelected = selectedDate && isSameDate(selectedDate, date);
          
          return (
            <div
              key={date.toISOString()}
              className={`rounded py-1 ${
                isDisabled 
                  ? 'text-gray-400' 
                  : 'cursor-pointer hover:bg-gray-200'
              } ${
                isSelected ? 'text-white' : ''
              }`}
              style={{
                backgroundColor: isSelected ? '#323B83' : 'transparent'
              }}
              onClick={() => handleDateClick(date)}
            >
              {date.getDate()}
            </div>
          );
        })}
      </div>
    </div>
  );
}